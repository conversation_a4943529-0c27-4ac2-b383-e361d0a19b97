import type { CheckboxChangeEvent } from 'antd/es/checkbox'
import { userStore } from '@/store/userStore'
import { cn } from '@/utils'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { Checkbox } from 'antd'
import clsx from 'clsx'
import { motion } from 'framer-motion'
import { useEffect, useState } from 'react'
import './TrendSelectionPage.module.css'

interface TrendTopic {
  id: string
  title: string
  isSelected?: boolean
}

interface TrendDetail {
  id: string
  content: string
  isReference: boolean
}

interface TrendSelectionPageProps {
  planning_report: string
  className: string
  handlerSelectTopic: (topicsId: string) => void
}

export const TrendSelectionPage: React.FC<TrendSelectionPageProps> = ({
  className,
  handlerSelectTopic = () => { },
  planning_report = '嗯，用户要的是卡姿兰「黑磁散粉」的新品社媒推广策划案，只不过现在先输出我的思考路径。先把整体框架对齐：市场→用户→竞品→品牌→策略→内容→节奏。接下来把四份资料拆解成可用信息池。\\n\\n1. 市场层面：行业报告给到全球6380亿美元、APAC贡献70%、中国增速12%这些硬数据；再结合小红书7日3.8万篇笔记、平均CPM≈160的低成本窗口，可直接作为“市场机会”论据。我要把“线上渠道渗透+内容成本洼地”写进机会点。\\n\\n2. 用户层面：品牌画像18-30岁Z世代+小红书洞察18-24岁集中64.7%，痛点关键词“油皮/持妆/早八”与场景“通勤/学生党”高度重叠；复购、价格敏感、情绪价值（不补妆自由感）是决策关键。我要在洞察里把“功能+情绪+社交”三层需求模型拆开，再埋下后面内容打法的钩子。\\n\\n3. 竞品：横向有花西子、完美日记，散粉赛道里橘朵、谜尚声量更高。差异点在于黑磁有专利“磁力吸油+12h控油”。我要把竞品在小红书缺少“平价×高功效散粉”内容空窗标成突破口，同时提示兰蔻等高端品牌未下场——便于后面做“科技平替”定位。\\n\\n4. 品牌洞察：卡姿兰现状是技术壁垒强但小红书短视频占比低、CPE高。策略里必须强调“短视频+素人矩阵”补短板，并把“磁吸黑科技”视觉化。\\n\\n5. 营销策略：差异化锚点＝“平价×黑科技×长效控油”。人群分层：核心油皮学生→查询→对比→潜在。渠道优先小红书，其次抖音补足声量，考虑B站做深测评背书。预算如果未知默认按“内容占60%/达人占30%/投流10%”写一版，可灵活删改。\\n\\n6. 内容规划：要给出三阶段Roadmap：预热（功能科普+磁吸实验）→爆发（素人8h对比挑战+KOL种草）→巩固（UGC二创+混剪测评）。素材方向至少列四条：功能实验、通勤Vlog、黄黑皮前后对比、闺蜜拼单开箱。达人用ACC10-11腰部+长尾素人支撑CPM优势，再挑2-3位彩妆KOL做声量引爆。\\n\\n7. 数据及风险：所有数字引用Euromonitor、Statista、天猫生意参谋、小红书官方后台；要在附件里列出处。风险点写CPM抬价、KOL翻车、舆情（防止黑科技被质疑成分安全），对应预案是备用达人池+实验室权威背书+实时监测SOP。\\n\\n最后自检：逻辑闭环要确保“行业机会→油皮痛点→黑磁差异→小红书低成本窗口”线索贯穿；KPI要能落地（3个月内容渗透率TOP3、CPE降50%）。这样整份策划案就既有宏观论据又有落地打法，差异化卖点与平台特性形成强耦合。',
}) => {
  const token = userStore.token
  const [topics, setTopics] = useState<TrendTopic[]>([{
    id: '1',
    title: '测试1',
    isSelected: false,
  }, {
    id: '2',
    title: '测试2',
    isSelected: false,
  }])
  const [topicsDetails, setTopicsDetails] = useState<Omit<TrendDetail, 'isReference'>[]>([
    {
      id: '1',
      content: '测试1内容',
    },
    {
      id: '2',
      content: '测试2内容',
    },
  ])
  const [preview, setPreview] = useState<TrendDetail>()
  const [previewTopicId, setPreviewTopicId] = useState<string>('')
  const [selectedTopic, setSelectTopic] = useState<string>('') // 选择为我的热点话题的ID
  const handlerPreviewTopic = (topicId: string) => {
    setPreviewTopicId(topicId)
    const item = topicsDetails.find(item => item.id === topicId) as TrendDetail
    setPreview({
      ...item,
    })
    /** 选择话题时，自动滚动到右侧详情区域（移动端） */
    if (window.innerWidth < 1024) {
      setTimeout(() => {
        const detailElement = document.getElementById('trend-detail-section')
        detailElement?.scrollIntoView({ behavior: 'smooth', block: 'start' })
      }, 100)
    }
  }

  const fetchHotPots = async () => {
    await fetchEventSource(`http://192.168.112.171:8080/api/app/market/stream/execute-main`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({
        taskInstanceId: '1958473114904039424', // 写死id
        platform: 'rednote',
        workflowName: 'hotpots_analysis',
        parameters: {
          platform: 'rednote',
          planning_report,
        },
      }),
      onmessage(ev) {
        if (!ev.data || ev.event === 'end')
          return

        const parsed = JSON.parse(ev.data)
        const topicTitleList = ['topic1', 'topic2', 'topic3', 'topic4', 'topic5']
        const topicDetailNodeTitle = ['topic1_detail', 'topic2_detail', 'topic3_detail', 'topic4_detail', 'topic5_detail']
        if (topicTitleList.includes(parsed.node_title)) {
          setTopics(prev => [
            ...prev,
            {
              id: parsed.node_title,
              title: parsed.content,
            },
          ])
        }
        if (topicDetailNodeTitle.includes(parsed.node_title)) {
          const topicMap = {
            topic1_detail: 'topic1',
            topic2_detail: 'topic2',
            topic3_detail: 'topic3',
            topic4_detail: 'topic4',
            topic5_detail: 'topic5',
          }
          setTopicsDetails(prev => [
            ...prev,
            {
              id: topicMap[parsed.node_title as keyof typeof topicMap],
              content: parsed.content,
            },
          ],
          )
        }
      },
      onclose() {
        /**
         * 响应完成
         * 默认显示第一个热点话题的内容
         */
        const item = topicsDetails.find(item => item.id === 'topic1') as TrendDetail
        setPreview({
          ...item,
        })
      },
    })
  }

  const handleReferenceToggle = (e: CheckboxChangeEvent, id: string) => {
    const { checked } = e.target
    id = checked
      ? id
      : ''
    handlerSelectTopic(id) // 回传给上层
    setSelectTopic(id)
  }

  useEffect(() => {
    // fetchHotPots()
  }, [])

  return (
    <div className={ cn('w-full max-w-7xl mx-auto p-6 space-y-8', className) }>
      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-2xl text-gray-900 font-semibold dark:text-gray-100">
          Choose either one trending topic or a reference post
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          We will create your posts based on your choice
        </p>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Left Side - Trending Topics */}
        <div className="space-y-4">

          <div className="space-y-3">
            {topics.map((topic, index) => (
              <motion.div
                key={ topic.id }
                initial={ { opacity: 0, y: 20 } }
                animate={ { opacity: 1, y: 0 } }
                transition={ { duration: 0.3, delay: index * 0.1 } }
                className={ clsx(`[background-clip:padding-box,border-box] [background-origin:padding-box,border-box] relative h-[74px] cursor-pointer overflow-hidden border-[1.5px] border-[#D9D9D9] rounded-[8px] transition-all duration-300 hover:border-[#DD9DFF] hover:shadow-md`, previewTopicId === topic.id && 'border-[#DD9DFF] shadow-md') }
                onClick={ () => handlerPreviewTopic(topic.id) }
              >
                {selectedTopic === topic.id && <span className="absolute right-[12px] top-[12px] items-center rounded-full bg-gray-100 px-2 py-0.5 text-sm text-gray-700">
                  Selected
                </span>}

                <div
                  className="relative ml-[14px] leading-[74px] transition-all duration-300"
                >
                  <h3 className="mb-1 text-gray-900 font-medium dark:text-gray-100">
                    {topic.title}
                  </h3>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Right Side - Trend Detail */}
        <div id="trend-detail-section" className="space-y-4">
          {preview
            && <motion.div
              initial={ { opacity: 0, x: 20 } }
              animate={ { opacity: 1, x: 0 } }
              transition={ { duration: 0.4 } }
              className="[background-clip:padding-box,border-box] [background-image:linear-gradient(to_right,#fff,#fff),linear-gradient(90deg,#DD9DFF_0%,#36D3FF_100%)] [background-origin:padding-box,border-box] relative overflow-hidden border-[1.5px] border-transparent rounded-2xl transition-all duration-300"
            >
              <div
                className="h-[410px] p-[16px] transition-all duration-300 space-y-4"
                style={ {
                  background: preview.id === selectedTopic
                    ? 'linear-gradient(90deg, rgba(221, 157, 255, 0.10) 0%, rgba(54, 211, 255, 0.10) 100%)'
                    : 'transparent',
                } }
              >
                <div className="h-[calc(100%-39px)] overflow-auto space-y-3">
                  <p className="text-gray-700 leading-relaxed dark:text-gray-300">
                    {preview.content}
                  </p>
                </div>

                {/* 复选框在面板内部 */}
                <div className="w-full flex items-center justify-end gap-3 dark:border-gray-600">
                  <Checkbox onChange={ e => handleReferenceToggle(e, preview.id) } checked={ preview.id === selectedTopic }>Select as my reference</Checkbox>
                </div>
              </div>
            </motion.div>}
        </div>
      </div>

    </div>
  )
}

export default TrendSelectionPage
