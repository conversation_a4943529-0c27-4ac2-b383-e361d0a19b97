/**
 * Trend 趋势分析页面主组件
 *
 * 功能：
 * - 提供趋势分析的完整工作流
 * - 支持表单模式和聊天模式
 * - 集成 AI 代理进行智能分析
 * - 支持历史记录管理
 */

import type { TrendStep1Params } from '@/api/TrendApi'
import type { AIAgent } from '../ChatV2/components/TopBar'
import { TrendApi } from '@/api/TrendApi'
import { GlowBorder } from '@/components/Card'
import { Loading } from '@/components/Loading'
import { motion } from 'framer-motion'
import { ArrowLeft } from 'lucide-react'
import { useState } from 'react'

import { ExtGuide } from '../ChatV2/components/ExtGuide'
import TopBar from '../ChatV2/components/TopBar'
import { ChatPage } from './components/ChatPage'
import TrendWelcome from './components/TrendWelcome'
import { trendAg, trendHi } from './stores'
import { handleFormSubmit } from './stores/chatActions'
import { handleTrendAgentError } from './stores/error'

/** 设置为模拟数据模式 */
TrendApi.isMock = true

const {
  mdToCodePreview,
  messageStore,
  reportStore,
  resetTrendStore,
  stateStore,
  stepState,
  taskStore,
} = trendAg

const {
  mdToCodePreview: hiMdToCodePreview,
  messageStore: hiMessageStore,
  reportStore: hiReportStore,
  resetTrendStore: resetHiStore,
  stateStore: hiStateStore,
  stepState: hiStepState,
  taskStore: hiTaskStore,
} = trendHi

function App() {
  const [viewMode, setViewMode] = useState<'agent' | 'history'>('agent')
  const { mode } = stateStore.useAndDispose()
  const { isProcessing } = stateStore.useAndDispose()
  const { isLoading } = hiStateStore.useAndDispose()

  /** 前置流程状态管理 */
  const [showWelcome, setShowWelcome] = useState(true)
  const [isGenerating, setIsGenerating] = useState(false)
  const [message, setMessage] = useState('')
  const [uploadedImage, setUploadedImage] = useState<string | null>(null)
  const [urlInputValue, setUrlInputValue] = useState('')
  const [selectedButton, setSelectedButton] = useState<string | null>(null)

  // TopBar 状态管理
  const [isTopBarDropdownExpanded, setIsTopBarDropdownExpanded] = useState(false)

  /** 固定的 4 个 Bot - 与 ChatV2 保持一致 */
  const fixedAgents: AIAgent[] = [
    { id: 'research-analyst', name: 'Research Analyst', icon: '/src/assets/image/home/<USER>' },
    { id: 'brand-strategist', name: 'Brand Strategist', icon: '/src/assets/image/home/<USER>' },
    { id: 'creative-director', name: 'Creative Director', icon: '/src/assets/image/home/<USER>' },
    { id: 'operations-manager', name: 'Operations Manager', icon: '/src/assets/image/home/<USER>' },
  ]

  // TopBar Agent 点击事件（暂时无操作）
  const handleTopBarAgentClick = (_agent: AIAgent) => {
    /** 暂时不执行任何操作，保持 4 个 bot 固定显示 */
  }

  /** 开始 AI 分析的处理函数 */
  const handleStartAIAnalysis = async (formData: TrendStep1Params) => {
    try {
      await handleFormSubmit(formData, trendAg)
    }
    catch (error) {
      console.error('AI analysis failed:', error)
      handleTrendAgentError(true)
    }
  }

  function handleBackToAgentClick() {
    setViewMode('agent')
  }

  /** 前置流程事件处理 */
  function handleImageUpload(fileOrUrl: File | string) {
    // 如果是字符串（URL），直接设置
    if (typeof fileOrUrl === 'string') {
      setUploadedImage(fileOrUrl)
    }
    // 如果是 File 对象，读取为 base64
    else if (fileOrUrl instanceof File) {
      const reader = new FileReader()
      reader.onload = (e) => {
        setUploadedImage(e.target?.result as string)
      }
      reader.readAsDataURL(fileOrUrl)
    }
  }

  function handleImageRemove() {
    setUploadedImage(null)
  }

  async function handleGenerate() {
    if (!message.trim())
      return

    setIsGenerating(true)
    setShowWelcome(false)

    try {
      /** 设置为聊天模式，直接进入 ChatPage */
      stateStore.mode = 'chat'

      /** 设置 ChatPage 的初始状态为 ChatV2 流程模式 */
      stateStore.chatV2FlowMode = 'thinking' // 新增状态
      stateStore.userDescription = message // 保存用户描述
      stateStore.uploadedImage = uploadedImage // 保存上传的图片
    }
    catch (error) {
      console.error('Generate failed:', error)
      handleTrendAgentError(true)
    }
    finally {
      setIsGenerating(false)
    }
  }

  return (
    <>
      {/* TopBar 只在前置页面显示 */}
      {showWelcome && viewMode === 'agent' && (
        <TopBar
          agents={ fixedAgents }
          onAgentClick={ handleTopBarAgentClick }
          dropdownExpanded={ isTopBarDropdownExpanded }
          onDropdownToggle={ setIsTopBarDropdownExpanded }
          containerClassName="fixed left-0 right-0 top-0 z-50"
        />
      )}

      {showWelcome && viewMode === 'agent'
        ? (
            // Welcome 前置界面
            <motion.div
              key="welcome"
              initial={ { opacity: 0 } }
              animate={ { opacity: 1 } }
              exit={ { opacity: 0 } }
              transition={ { duration: 0.3 } }
              className="h-full w-full"
            >
              <TrendWelcome
                message={ message }
                onMessageChange={ setMessage }
                uploadedImage={ uploadedImage }
                onImageUpload={ handleImageUpload }
                onImageRemove={ handleImageRemove }
                urlInputValue={ urlInputValue }
                onUrlInputChange={ setUrlInputValue }
                selectedButton={ selectedButton }
                onButtonSelect={ setSelectedButton }
                onGenerate={ handleGenerate }
                isGenerating={ isGenerating }
                inputPlaceholder="Enter a description of your brand and product for trend analysis"
              />
            </motion.div>
          )
        : viewMode === 'history' || (viewMode === 'agent' && mode === 'chat')
          ? (
              <motion.div
                key="chat"
                initial={ { opacity: 0 } }
                animate={ { opacity: 1 } }
                exit={ { opacity: 0 } }
                transition={ { duration: 0.3 } }
                className="relative h-full w-full bg-[#EDF4FF]"

              >
                { viewMode === 'history' && isProcessing && (
                  <GlowBorder
                    className="group absolute left-2 top-4 z-50 cursor-pointer rounded-full transition-all duration-300 hover:opacity-50"
                    onClick={ handleBackToAgentClick }
                  >
                    <div className="flex items-center gap-2 bg-white px-3 py-1.5 text-sm font-medium">
                      <ArrowLeft
                        size={ 16 }
                        strokeWidth={ 1.5 }
                        className="transition-transform duration-300 group-hover:-translate-x-1"
                      />
                      Back
                    </div>
                  </GlowBorder>
                ) }

                <Loading loading={ isLoading } />

                <ChatPage
                  className="h-full flex-1"
                  style={ {
                    display: viewMode === 'agent'
                      ? 'flex'
                      : 'none',
                  } }
                  taskStore={ taskStore }
                  messageStore={ messageStore }
                  mdToCodePreview={ mdToCodePreview }
                  resetDistributionStore={ resetTrendStore }
                  reportStore={ reportStore }
                  stepState={ stepState }
                  stateStore={ stateStore }
                  showTopBar={ viewMode === 'agent' && !showWelcome }
                  topBarAgents={ fixedAgents }
                  onTopBarAgentClick={ handleTopBarAgentClick }
                  topBarDropdownExpanded={ isTopBarDropdownExpanded }
                  onTopBarDropdownToggle={ setIsTopBarDropdownExpanded }
                  onStartAIAnalysis={ handleStartAIAnalysis }
                />

                { viewMode === 'history' && <ChatPage
                  className="h-full flex-1"
                  taskStore={ hiTaskStore }
                  messageStore={ hiMessageStore }
                  mdToCodePreview={ hiMdToCodePreview }
                  resetDistributionStore={ resetHiStore }
                  reportStore={ hiReportStore }
                  stepState={ hiStepState }
                  stateStore={ hiStateStore }
                /> }
              </motion.div>
            )
          : null }

      <ExtGuide />
    </>
  )
}

export default App
